'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import Navigation from '../components/Navigation';

export default function ProfilePage() {
  const { user, updateUser, isAuthenticated, isLoading } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    facebookUrl: '',
    gender: '',
    age: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  // Phone verification states
  const [isPhoneModalOpen, setIsPhoneModalOpen] = useState(false);
  const [newPhone, setNewPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // Password change states
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        facebookUrl: user.facebookUrl || '',
        gender: user.gender || '',
        age: user.age || ''
      });
    }
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        updateUser(data.user);
        setIsEditing(false);
        setMessage({ type: 'success', text: 'პროფილი წარმატებით განახლდა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'პროფილის განახლება ვერ მოხერხდა' });
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setMessage({ type: 'error', text: 'პროფილის განახლება ვერ მოხერხდა' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setMessage({ type: '', text: '' });
    // Reset form data to original user data
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        facebookUrl: user.facebookUrl || '',
        gender: user.gender || '',
        age: user.age || ''
      });
    }
  };

  // Phone verification functions
  const handlePhoneChange = () => {
    setIsPhoneModalOpen(true);
    setNewPhone('');
    setVerificationCode('');
    setIsVerificationSent(false);
  };

  const sendPhoneVerification = async () => {
    if (!newPhone || newPhone.length < 9) {
      setMessage({ type: 'error', text: 'გთხოვთ შეიყვანოთ სწორი ტელეფონის ნომერი' });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/auth/send-phone-verification', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone: newPhone })
      });

      const data = await response.json();
      if (data.success) {
        setIsVerificationSent(true);
        setMessage({ type: 'success', text: 'ვერიფიკაციის კოდი გაიგზავნა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'კოდის გაგზავნა ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'კოდის გაგზავნა ვერ მოხერხდა' });
    } finally {
      setIsVerifying(false);
    }
  };

  const verifyPhoneChange = async () => {
    if (verificationCode.length !== 6) {
      setMessage({ type: 'error', text: 'გთხოვთ შეიყვანოთ 6-ნიშნა კოდი' });
      return;
    }

    setIsVerifying(true);
    try {
      const response = await fetch('/api/auth/verify-phone-change', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phone: newPhone,
          verificationCode
        })
      });

      const data = await response.json();
      if (data.success) {
        updateUser(data.user);
        setIsPhoneModalOpen(false);
        setMessage({ type: 'success', text: 'ტელეფონის ნომერი წარმატებით შეიცვალა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'ვერიფიკაცია ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'ვერიფიკაცია ვერ მოხერხდა' });
    } finally {
      setIsVerifying(false);
    }
  };

  // Password change functions
  const handlePasswordChange = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'გთხოვთ შეავსოთ ყველა ველი' });
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setMessage({ type: 'error', text: 'ახალი პაროლები არ ემთხვევა' });
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setMessage({ type: 'error', text: 'პაროლი უნდა იყოს მინიმუმ 6 სიმბოლო' });
      return;
    }

    setIsChangingPassword(true);
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });

      const data = await response.json();
      if (data.success) {
        setIsPasswordModalOpen(false);
        setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
        setMessage({ type: 'success', text: 'პაროლი წარმატებით შეიცვალა' });
      } else {
        setMessage({ type: 'error', text: data.message || 'პაროლის შეცვლა ვერ მოხერხდა' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'პაროლის შეცვლა ვერ მოხერხდა' });
    } finally {
      setIsChangingPassword(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh] pt-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              ავტორიზაცია საჭიროა
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              პროფილის სანახავად გთხოვთ შეხვიდეთ სისტემაში
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
      {/* Paranormal Background */}
      <div className="absolute inset-0 opacity-5 dark:opacity-10">
        <svg className="w-full h-full" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
          {/* Spider Web Pattern */}
          <defs>
            <pattern id="spiderWeb" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
              {/* Web center */}
              <circle cx="100" cy="100" r="2" fill="currentColor" className="text-mystery dark:text-mystery-light"/>

              {/* Radial lines */}
              <line x1="100" y1="100" x2="100" y2="20" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="150" y2="50" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="180" y2="100" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="150" y2="150" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="100" y2="180" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="50" y2="150" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="20" y2="100" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>
              <line x1="100" y1="100" x2="50" y2="50" stroke="currentColor" strokeWidth="0.5" className="text-mystery dark:text-mystery-light"/>

              {/* Concentric circles */}
              <circle cx="100" cy="100" r="30" fill="none" stroke="currentColor" strokeWidth="0.3" className="text-mystery dark:text-mystery-light"/>
              <circle cx="100" cy="100" r="50" fill="none" stroke="currentColor" strokeWidth="0.3" className="text-mystery dark:text-mystery-light"/>
              <circle cx="100" cy="100" r="70" fill="none" stroke="currentColor" strokeWidth="0.3" className="text-mystery dark:text-mystery-light"/>

              {/* Small decorative elements */}
              <circle cx="60" cy="60" r="1" fill="currentColor" className="text-accent dark:text-accent-light"/>
              <circle cx="140" cy="140" r="1" fill="currentColor" className="text-accent dark:text-accent-light"/>
              <circle cx="140" cy="60" r="1" fill="currentColor" className="text-accent dark:text-accent-light"/>
              <circle cx="60" cy="140" r="1" fill="currentColor" className="text-accent dark:text-accent-light"/>
            </pattern>
          </defs>

          <rect width="100%" height="100%" fill="url(#spiderWeb)"/>

          {/* Floating mystical orbs */}
          <g className="animate-pulse">
            <circle cx="200" cy="150" r="3" fill="currentColor" className="text-mystery-light opacity-30">
              <animate attributeName="cy" values="150;130;150" dur="4s" repeatCount="indefinite"/>
            </circle>
            <circle cx="800" cy="300" r="2" fill="currentColor" className="text-accent-light opacity-40">
              <animate attributeName="cy" values="300;280;300" dur="3s" repeatCount="indefinite"/>
            </circle>
            <circle cx="1000" cy="200" r="2.5" fill="currentColor" className="text-mystery opacity-35">
              <animate attributeName="cy" values="200;180;200" dur="5s" repeatCount="indefinite"/>
            </circle>
          </g>
        </svg>
      </div>

      <Navigation />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-24 pb-12 relative z-10">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            ჩემი პროფილი
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            მართეთ თქვენი პირადი ინფორმაცია და ანგარიშის პარამეტრები
          </p>
        </div>

        <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Profile Header */}
          <div className="px-6 py-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-semibold">
                  {user?.avatar ? (
                    <img src={user.avatar} alt={user.name} className="w-16 h-16 rounded-full object-cover" />
                  ) : (
                    user?.name?.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2) ||
                    (user?.firstName && user?.lastName ? `${user.firstName[0]}${user.lastName[0]}` : 'U')
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {user?.firstName && user?.lastName
                      ? `${user.firstName} ${user.lastName}`
                      : user?.name || 'მომხმარებელი'
                    }
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {user?.phone || 'ტელეფონი არ არის მითითებული'}
                  </p>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      ბალანსი: ₾{user?.balance?.toFixed(2) || '0.00'}
                    </span>
                    {user?.isVerified && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        ✓ ვერიფიცირებული
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    რედაქტირება
                  </button>
                ) : (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
                    >
                      {isSaving ? 'შენახვა...' : 'შენახვა'}
                    </button>
                    <button
                      onClick={handleCancel}
                      className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      გაუქმება
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {message.text && (
              <div className={`mb-6 p-4 rounded-lg ${
                message.type === 'success'
                  ? 'bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
                  : 'bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800'
              }`}>
                {message.text}
              </div>
            )}

            <div className="space-y-8">
              {/* Personal Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                  პირადი ინფორმაცია
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      სახელი
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-white py-2">{user?.firstName || '-'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      გვარი
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-white py-2">{user?.lastName || '-'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ელ-ფოსტა
                    </label>
                    <p className="text-gray-900 dark:text-white py-2">{user?.email || '-'}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      ელ-ფოსტის შეცვლისთვის დაგვიკავშირდით
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ტელეფონი
                    </label>
                    <div className="flex items-center justify-between">
                      <p className="text-gray-900 dark:text-white py-2">{user?.phone || '-'}</p>
                      <button
                        onClick={handlePhoneChange}
                        className="text-sm text-primary hover:text-primary-dark font-medium transition-colors"
                      >
                        შეცვლა
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                  დამატებითი ინფორმაცია
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Facebook URL
                    </label>
                    {isEditing ? (
                      <input
                        type="url"
                        name="facebookUrl"
                        value={formData.facebookUrl}
                        onChange={handleInputChange}
                        placeholder="https://facebook.com/username"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-white py-2">{user?.facebookUrl || '-'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      სქესი
                    </label>
                    {isEditing ? (
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      >
                        <option value="">აირჩიეთ სქესი</option>
                        <option value="male">მამრობითი</option>
                        <option value="female">მდედრობითი</option>
                        <option value="other">სხვა</option>
                      </select>
                    ) : (
                      <p className="text-gray-900 dark:text-white py-2">
                        {user?.gender === 'male' ? 'მამრობითი' :
                         user?.gender === 'female' ? 'მდედრობითი' :
                         user?.gender === 'other' ? 'სხვა' : '-'}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ასაკი
                    </label>
                    {isEditing ? (
                      <input
                        type="number"
                        name="age"
                        value={formData.age}
                        onChange={handleInputChange}
                        min="16"
                        max="100"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    ) : (
                      <p className="text-gray-900 dark:text-white py-2">{user?.age || '-'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      რეგისტრაციის თარიღი
                    </label>
                    <p className="text-gray-900 dark:text-white py-2">
                      {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('ka-GE') : '-'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Security Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
                  უსაფრთხოება
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      პაროლი
                    </label>
                    <div className="flex items-center justify-between">
                      <p className="text-gray-900 dark:text-white py-2">••••••••</p>
                      <button
                        onClick={() => setIsPasswordModalOpen(true)}
                        className="text-sm text-primary hover:text-primary-dark font-medium transition-colors"
                      >
                        შეცვლა
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons - ახლა ეს ღილაკები header-ში არის */}
          </div>
        </div>
      </div>

      {/* Phone Verification Modal */}
      {isPhoneModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              ტელეფონის ნომრის შეცვლა
            </h3>

            {!isVerificationSent ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ახალი ტელეფონის ნომერი
                  </label>
                  <input
                    type="tel"
                    value={newPhone}
                    onChange={(e) => setNewPhone(e.target.value)}
                    placeholder="5XXXXXXXX"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setIsPhoneModalOpen(false)}
                    className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    გაუქმება
                  </button>
                  <button
                    onClick={sendPhoneVerification}
                    disabled={isVerifying}
                    className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isVerifying ? 'გაგზავნა...' : 'კოდის გაგზავნა'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    ვერიფიკაციის კოდი გაიგზავნა ნომერზე:
                  </p>
                  <p className="font-semibold text-gray-800 dark:text-white mb-6">
                    {newPhone}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                    შეიყვანეთ 6-ნიშნა კოდი
                  </label>
                  <input
                    type="text"
                    maxLength="6"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                    className="verification-input w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white text-center text-lg font-bold letter-spacing-wide"
                    placeholder="123456"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setIsPhoneModalOpen(false);
                      setIsVerificationSent(false);
                    }}
                    className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                  >
                    გაუქმება
                  </button>
                  <button
                    onClick={verifyPhoneChange}
                    disabled={isVerifying || verificationCode.length !== 6}
                    className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                  >
                    {isVerifying ? 'დადასტურება...' : 'დადასტურება'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {isPasswordModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              პაროლის შეცვლა
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  მიმდინარე პაროლი
                </label>
                <input
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ახალი პაროლი
                </label>
                <input
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ახალი პაროლის დადასტურება
                </label>
                <input
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => {
                    setIsPasswordModalOpen(false);
                    setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
                  }}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  გაუქმება
                </button>
                <button
                  onClick={handlePasswordChange}
                  disabled={isChangingPassword || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                  className="flex-1 px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  {isChangingPassword ? 'შეცვლა...' : 'შეცვლა'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
